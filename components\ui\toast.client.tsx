'use client';

import * as React from 'react';
import { Toast } from '@base-ui-components/react/toast';
import { cn } from '../../lib/utils';

// Create a global toast manager
export const toastManager = Toast.createToastManager();

// Toast Provider Component
export function ToastProvider({ children }: { children: React.ReactNode }) {
  return (
    <Toast.Provider toastManager={toastManager}>
      {children}
      <Toast.Portal>
        <Toast.Viewport className="fixed bottom-[1rem] right-[1rem] mx-auto flex w-[300px] sm:right-[2rem] sm:bottom-[2rem] sm:w-[350px]">
          <ToastList />
        </Toast.Viewport>
      </Toast.Portal>
    </Toast.Provider>
  );
}

// Toast List Component
function ToastList() {
  const { toasts } = Toast.useToastManager();
  
  return toasts.map((toast) => (
    <Toast.Root
      key={toast.id}
      toast={toast}
      className={cn(
        // Base positioning and stacking
        "absolute right-0 bottom-0 left-auto z-[calc(1000-var(--toast-index))] mr-0 w-full",
        // Transform with stacking and swipe
        "[transform:translateX(var(--toast-swipe-movement-x))_translateY(calc(var(--toast-swipe-movement-y)+calc(min(var(--toast-index),10)*-15px)))_scale(calc(max(0,1-(var(--toast-index)*0.1))))]",
        // Base styling
        "rounded-lg border bg-clip-padding p-4 shadow-lg transition-all [transition-property:opacity,transform] duration-500 ease-[cubic-bezier(0.22,1,0.36,1)] select-none",
        // Gap after element
        "after:absolute after:bottom-full after:left-0 after:h-[calc(var(--gap)+1px)] after:w-full after:content-['']",
        // Theme-based colors
        "border-gray-200 bg-white text-gray-900 dark:border-gray-700 dark:bg-[#212026] dark:text-white",
        // Animation states
        "data-[ending-style]:opacity-0",
        "data-[expanded]:[transform:translateX(var(--toast-swipe-movement-x))_translateY(calc(var(--toast-offset-y)*-1+calc(var(--toast-index)*var(--gap)*-1)+var(--toast-swipe-movement-y)))]",
        "data-[limited]:opacity-0",
        "data-[starting-style]:[transform:translateY(150%)]",
        // Swipe direction animations
        "data-[ending-style]:data-[swipe-direction=down]:[transform:translateY(calc(var(--toast-swipe-movement-y)+150%))]",
        "data-[expanded]:data-[ending-style]:data-[swipe-direction=down]:[transform:translateY(calc(var(--toast-swipe-movement-y)+150%))]",
        "data-[ending-style]:data-[swipe-direction=left]:[transform:translateX(calc(var(--toast-swipe-movement-x)-150%))_translateY(var(--offset-y))]",
        "data-[expanded]:data-[ending-style]:data-[swipe-direction=left]:[transform:translateX(calc(var(--toast-swipe-movement-x)-150%))_translateY(var(--offset-y))]",
        "data-[ending-style]:data-[swipe-direction=right]:[transform:translateX(calc(var(--toast-swipe-movement-x)+150%))_translateY(var(--offset-y))]",
        "data-[expanded]:data-[ending-style]:data-[swipe-direction=right]:[transform:translateX(calc(var(--toast-swipe-movement-x)+150%))_translateY(var(--offset-y))]",
        "data-[ending-style]:data-[swipe-direction=up]:[transform:translateY(calc(var(--toast-swipe-movement-y)-150%))]",
        "data-[expanded]:data-[ending-style]:data-[swipe-direction=up]:[transform:translateY(calc(var(--toast-swipe-movement-y)-150%))]",
        "data-[ending-style]:[&:not([data-limited])]:[transform:translateY(150%)]"
      )}
      style={{
        ['--gap' as string]: '1rem',
        ['--offset-y' as string]:
          'calc(var(--toast-offset-y) * -1 + (var(--toast-index) * var(--gap) * -1) + var(--toast-swipe-movement-y))',
      }}
    >
      <div className="flex items-start gap-3">
        {/* Icon based on toast type */}
        <ToastIcon type={toast.type} />
        
        <div className="flex-1 min-w-0">
          <Toast.Title className="text-sm font-medium leading-5 font-manrope_1" />
          <Toast.Description className="text-sm leading-5 text-gray-600 dark:text-gray-300 font-manrope_1 mt-1" />
        </div>
        
        <Toast.Close
          className="flex h-5 w-5 items-center justify-center rounded border-none bg-transparent text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-200 transition-colors"
          aria-label="Close"
        >
          <XIcon className="h-4 w-4" />
        </Toast.Close>
      </div>
    </Toast.Root>
  ));
}

// Toast Icon Component
function ToastIcon({ type }: { type?: string }) {
  switch (type) {
    case 'success':
      return (
        <div className="flex h-5 w-5 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30">
          <CheckIcon className="h-3 w-3 text-green-600 dark:text-green-400" />
        </div>
      );
    case 'error':
      return (
        <div className="flex h-5 w-5 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30">
          <XIcon className="h-3 w-3 text-red-600 dark:text-red-400" />
        </div>
      );
    case 'warning':
      return (
        <div className="flex h-5 w-5 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/30">
          <AlertIcon className="h-3 w-3 text-yellow-600 dark:text-yellow-400" />
        </div>
      );
    case 'info':
      return (
        <div className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30">
          <InfoIcon className="h-3 w-3 text-blue-600 dark:text-blue-400" />
        </div>
      );
    default:
      return (
        <div className="flex h-5 w-5 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700">
          <InfoIcon className="h-3 w-3 text-gray-600 dark:text-gray-300" />
        </div>
      );
  }
}

// Helper functions for creating toasts
export const toast = {
  success: (title: string, options?: { description?: string }) => {
    return toastManager.add({
      title,
      description: options?.description,
      type: 'success',
    });
  },
  
  error: (title: string, options?: { description?: string }) => {
    return toastManager.add({
      title,
      description: options?.description,
      type: 'error',
    });
  },
  
  warning: (title: string, options?: { description?: string }) => {
    return toastManager.add({
      title,
      description: options?.description,
      type: 'warning',
    });
  },
  
  info: (title: string, options?: { description?: string }) => {
    return toastManager.add({
      title,
      description: options?.description,
      type: 'info',
    });
  },
  
  message: (title: string, options?: { description?: string }) => {
    return toastManager.add({
      title,
      description: options?.description,
    });
  },
};

// Icons
function XIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M18 6 6 18" />
      <path d="m6 6 12 12" />
    </svg>
  );
}

function CheckIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M20 6 9 17l-5-5" />
    </svg>
  );
}

function AlertIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z" />
      <path d="M12 9v4" />
      <path d="m12 17 .01 0" />
    </svg>
  );
}

function InfoIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <circle cx="12" cy="12" r="10" />
      <path d="m12 16-4-4 4-4" />
      <path d="M16 12H8" />
    </svg>
  );
}
